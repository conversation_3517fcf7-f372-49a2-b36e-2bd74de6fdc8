'use server'
import { CallAPI } from '@/configs/axios/axios';
import { TJobDetail } from '@/features/careers/schemas/job-schema';

export const getJobDetail = async (id: string): Promise<TJobDetail | null> => {
	try {
		const { data, status } = await CallAPI({apiKey:true}).get(`/jobs/${id}`);

		if (status !== 200) {
			throw new Error('Failed to fetch job detail');
		}

		return data;
	} catch (error) {
		console.error('Error fetching job detail:', error);
		return null;
	}
};
